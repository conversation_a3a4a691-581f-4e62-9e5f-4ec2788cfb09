<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

$page_title = "لوحة تحكم العضو";

try {
    require_once '../includes/config.php';
    require_once '../includes/auth.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

// التحقق من وجود معرف المستخدم
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "يجب تسجيل الدخول أولاً";
    header("Location: ../index.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// الحصول على معلومات العضو
try {
    $sql = "SELECT * FROM members WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
    }
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user = $stmt->get_result()->fetch_assoc();
    $stmt->close();

    if (!$user) {
        throw new Exception("لم يتم العثور على بيانات العضو");
    }
} catch (Exception $e) {
    die("خطأ في جلب بيانات العضو: " . $e->getMessage());
}

// الحصول على الاشتراك النشط الحالي
$current_subscription = null;
try {
    $current_subscription_sql = "SELECT s.*, sp.name as plan_name, sp.duration_months
                                FROM subscriptions s
                                JOIN subscription_plans sp ON s.plan_id = sp.id
                                WHERE s.member_id = ? AND s.status = 'active'
                                ORDER BY s.end_date DESC LIMIT 1";
    $current_stmt = $conn->prepare($current_subscription_sql);
    if ($current_stmt) {
        $current_stmt->bind_param("i", $user_id);
        $current_stmt->execute();
        $current_subscription = $current_stmt->get_result()->fetch_assoc();
        $current_stmt->close();
    }
} catch (Exception $e) {
    // تجاهل الخطأ واستمر
    $current_subscription = null;
}

// الحصول على آخر دفعة
$last_payment = null;
try {
    $last_payment_sql = "SELECT * FROM payments WHERE member_id = ? ORDER BY payment_date DESC LIMIT 1";
    $payment_stmt = $conn->prepare($last_payment_sql);
    if ($payment_stmt) {
        $payment_stmt->bind_param("i", $user_id);
        $payment_stmt->execute();
        $last_payment = $payment_stmt->get_result()->fetch_assoc();
        $payment_stmt->close();
    }
} catch (Exception $e) {
    // تجاهل الخطأ واستمر
    $last_payment = null;
}

// حساب الأيام المتبقية
$days_remaining = 0;
if ($current_subscription) {
    try {
        $end_date = new DateTime($current_subscription['end_date']);
        $today = new DateTime();
        $interval = $today->diff($end_date);
        $days_remaining = $interval->days;
        if ($today > $end_date) {
            $days_remaining = -$days_remaining; // منتهي
        }
    } catch (Exception $e) {
        $days_remaining = 0;
    }
}

$nav_path = '../';
include_once '../includes/header.php';
?>

<div class="dashboard-header">
    <h1>مرحباً بك، <?php echo htmlspecialchars($user['full_name']); ?></h1>
    <p>هنا يمكنك إدارة اشتراكاتك وملفك الشخصي.</p>
</div>

<div class="dashboard-cards">
    <div class="card">
        <h3>حالة الاشتراك</h3>
        <?php if ($current_subscription): ?>
            <?php
            $status_class = 'active';
            $status_text = 'نشط';
            if ($days_remaining < 0) {
                $status_class = 'inactive';
                $status_text = 'منتهي';
            } elseif ($days_remaining <= 7) {
                $status_class = 'suspended';
                $status_text = 'ينتهي قريباً';
            }
            ?>
            <div class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></div>
            <p>ينتهي في: <?php echo $current_subscription['end_date']; ?></p>
        <?php else: ?>
            <div class="status inactive">لا يوجد اشتراك</div>
            <p><a href="subscriptions.php" class="btn" style="margin-top: 10px;">اشترك الآن</a></p>
        <?php endif; ?>
    </div>

    <div class="card">
        <h3>آخر دفعة</h3>
        <?php if ($last_payment): ?>
            <div class="number"><?php echo number_format($last_payment['amount'], 2); ?> ر.س</div>
            <p>بتاريخ: <?php echo $last_payment['payment_date']; ?></p>
        <?php else: ?>
            <div class="number">-</div>
            <p>لا توجد دفعات</p>
        <?php endif; ?>
    </div>

    <div class="card">
        <h3>المدة المتبقية</h3>
        <?php if ($current_subscription && $days_remaining >= 0): ?>
            <div class="number"><?php echo $days_remaining; ?> يوم</div>
        <?php elseif ($current_subscription && $days_remaining < 0): ?>
            <div class="number" style="color: var(--error-color);">منتهي</div>
        <?php else: ?>
            <div class="number">-</div>
        <?php endif; ?>
    </div>
</div>

<div class="page-header">
    <h2>الاشتراكات الخاصة بي</h2>
    <a href="subscriptions.php" class="btn">عرض الكل</a>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>نوع الاشتراك</th>
                <th>تاريخ البدء</th>
                <th>تاريخ الانتهاء</th>
                <th>المبلغ المدفوع</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php
            // جلب آخر 3 اشتراكات للعضو
            $subscriptions_sql = "SELECT s.*, sp.name as plan_name
                                 FROM subscriptions s
                                 JOIN subscription_plans sp ON s.plan_id = sp.id
                                 WHERE s.member_id = ?
                                 ORDER BY s.created_at DESC
                                 LIMIT 3";
            $subs_stmt = $conn->prepare($subscriptions_sql);
            $subs_stmt->bind_param("i", $user_id);
            $subs_stmt->execute();
            $subscriptions = $subs_stmt->get_result();

            if ($subscriptions->num_rows > 0):
                while ($subscription = $subscriptions->fetch_assoc()):
                    // تحديد حالة الاشتراك
                    $status_class = $subscription['status'];
                    $status_text = '';
                    switch($subscription['status']) {
                        case 'active': $status_text = 'نشط'; break;
                        case 'expired': $status_text = 'منتهي'; break;
                        case 'cancelled': $status_text = 'ملغي'; break;
                    }
            ?>
                <tr>
                    <td><?php echo htmlspecialchars($subscription['plan_name']); ?></td>
                    <td><?php echo $subscription['start_date']; ?></td>
                    <td><?php echo $subscription['end_date']; ?></td>
                    <td><?php echo number_format($subscription['amount_paid'], 2); ?> ر.س</td>
                    <td><span class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                    <td class="actions">
                        <?php if ($subscription['status'] == 'active'): ?>
                            <span style="color: var(--success-color); font-size: 12px;">
                                <i class="fas fa-check-circle"></i> ساري
                            </span>
                        <?php else: ?>
                            <a href="subscriptions.php" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                <i class="fas fa-plus"></i> تجديد
                            </a>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php
                endwhile;
                $subs_stmt->close();
            else:
            ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px;">
                        <i class="fas fa-credit-card" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                        <p>لا توجد اشتراكات بعد</p>
                        <a href="subscriptions.php" class="btn" style="margin-top: 15px;">
                            <i class="fas fa-plus"></i> اشترك الآن
                        </a>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- إشعارات مهمة -->
<?php if ($current_subscription && $days_remaining <= 7 && $days_remaining >= 0): ?>
    <div class="warning" style="margin-top: 30px;">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>تنبيه:</strong> اشتراكك سينتهي خلال <?php echo $days_remaining; ?> أيام.
        <a href="subscriptions.php" style="color: var(--warning-color); text-decoration: underline;">جدد اشتراكك الآن</a>
    </div>
<?php elseif ($current_subscription && $days_remaining < 0): ?>
    <div class="error" style="margin-top: 30px;">
        <i class="fas fa-times-circle"></i>
        <strong>انتهى الاشتراك:</strong> اشتراكك منتهي منذ <?php echo abs($days_remaining); ?> يوم.
        <a href="subscriptions.php" style="color: var(--error-color); text-decoration: underline;">جدد اشتراكك الآن</a>
    </div>
<?php endif; ?>

<?php include_once '../includes/footer.php'; ?>