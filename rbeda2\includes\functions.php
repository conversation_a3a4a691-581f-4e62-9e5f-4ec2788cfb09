<?php
// ملف الدوال المساعدة
function redirect($url) {
    header("Location: $url");
    exit;
}

function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function is_logged_in() {
    return isset($_SESSION['user_id']);
}

function is_admin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == true;
}

function display_error($error) {
    return '<div class="error">'.$error.'</div>';
}

function display_success($message) {
    return '<div class="success">'.$message.'</div>';
}
?>