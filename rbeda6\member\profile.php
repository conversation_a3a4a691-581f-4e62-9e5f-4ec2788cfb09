<?php
$page_title = "الملف الشخصي";
require_once '../includes/config.php';
require_once '../includes/auth.php';

$user_id = $_SESSION['user_id'];

// جلب بيانات العضو
$sql = "SELECT * FROM members WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
$stmt->close();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['full_name']);
    $phone = sanitize_input($_POST['phone_number']);
    $password = sanitize_input($_POST['password']);
    $confirm_password = sanitize_input($_POST['confirm_password']);

    // التحقق من صحة البيانات
    if (empty($name)) {
        $error = 'الاسم الكامل مطلوب';
    } elseif (!empty($password) && $password !== $confirm_password) {
        $error = 'كلمتا المرور غير متطابقتين';
    } elseif (!empty($password) && strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        // تحديث البيانات
        if (!empty($password)) {
            // تحديث مع كلمة المرور
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $update_sql = "UPDATE members SET full_name = ?, phone_number = ?, password = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("sssi", $name, $phone, $hashed_password, $user_id);
        } else {
            // تحديث بدون كلمة المرور
            $update_sql = "UPDATE members SET full_name = ?, phone_number = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ssi", $name, $phone, $user_id);
        }

        if ($update_stmt->execute()) {
            $_SESSION['user_name'] = $name;
            $success = 'تم تحديث الملف الشخصي بنجاح';

            // تحديث البيانات المعروضة
            $user['full_name'] = $name;
            $user['phone_number'] = $phone;
        } else {
            $error = 'حدث خطأ أثناء التحديث: ' . $conn->error;
        }
        $update_stmt->close();
    }
}

$nav_path = '../';
include_once '../includes/header.php';
?>

<div class="page-header">
    <h2>الملف الشخصي</h2>
    <a href="dashboard.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للوحة التحكم</a>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
    <!-- معلومات الحساب -->
    <div class="profile-info">
        <h3><i class="fas fa-user"></i> معلومات الحساب</h3>

        <div class="info-row">
            <span class="info-label">الاسم الكامل:</span>
            <span class="info-value"><?php echo htmlspecialchars($user['full_name']); ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">البريد الإلكتروني:</span>
            <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">رقم الهاتف:</span>
            <span class="info-value"><?php echo $user['phone_number'] ? htmlspecialchars($user['phone_number']) : 'غير محدد'; ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">تاريخ الانضمام:</span>
            <span class="info-value"><?php echo $user['join_date']; ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">حالة العضوية:</span>
            <span class="info-value">
                <span class="status <?php echo $user['status']; ?>">
                    <?php
                    switch($user['status']) {
                        case 'active': echo 'نشط'; break;
                        case 'inactive': echo 'غير نشط'; break;
                        case 'suspended': echo 'موقوف'; break;
                    }
                    ?>
                </span>
            </span>
        </div>
    </div>

    <!-- إحصائيات العضوية -->
    <div class="profile-info">
        <h3><i class="fas fa-chart-line"></i> إحصائيات العضوية</h3>

        <?php
        // إحصائيات الاشتراكات
        $stats_sql = "SELECT
                        COUNT(*) as total_subscriptions,
                        SUM(amount_paid) as total_paid,
                        MAX(end_date) as last_subscription_end
                      FROM subscriptions
                      WHERE member_id = ?";
        $stats_stmt = $conn->prepare($stats_sql);
        $stats_stmt->bind_param("i", $user_id);
        $stats_stmt->execute();
        $stats = $stats_stmt->get_result()->fetch_assoc();
        $stats_stmt->close();

        // الاشتراك النشط
        $active_sql = "SELECT COUNT(*) as active_count FROM subscriptions WHERE member_id = ? AND status = 'active'";
        $active_stmt = $conn->prepare($active_sql);
        $active_stmt->bind_param("i", $user_id);
        $active_stmt->execute();
        $active_count = $active_stmt->get_result()->fetch_assoc()['active_count'];
        $active_stmt->close();
        ?>

        <div class="info-row">
            <span class="info-label">إجمالي الاشتراكات:</span>
            <span class="info-value"><?php echo $stats['total_subscriptions'] ?: 0; ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">الاشتراكات النشطة:</span>
            <span class="info-value"><?php echo $active_count; ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">إجمالي المدفوعات:</span>
            <span class="info-value"><?php echo number_format($stats['total_paid'] ?: 0, 2); ?> ر.س</span>
        </div>

        <div class="info-row">
            <span class="info-label">آخر اشتراك ينتهي:</span>
            <span class="info-value"><?php echo $stats['last_subscription_end'] ?: 'لا يوجد'; ?></span>
        </div>

        <div class="info-row">
            <span class="info-label">مدة العضوية:</span>
            <span class="info-value">
                <?php
                $join_date = new DateTime($user['join_date']);
                $today = new DateTime();
                $interval = $join_date->diff($today);
                echo $interval->y . ' سنة و ' . $interval->m . ' شهر';
                ?>
            </span>
        </div>
    </div>
</div>

<!-- نموذج تحديث البيانات -->
<div class="form-container">
    <h3><i class="fas fa-edit"></i> تحديث البيانات الشخصية</h3>

    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="full_name">الاسم الكامل: *</label>
            <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
        </div>

        <div class="form-group">
            <label for="email">البريد الإلكتروني:</label>
            <input type="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" disabled>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                لا يمكن تغيير البريد الإلكتروني. اتصل بالإدارة إذا كنت تحتاج لتغييره.
            </small>
        </div>

        <div class="form-group">
            <label for="phone_number">رقم الهاتف:</label>
            <input type="tel" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($user['phone_number']); ?>">
        </div>

        <div class="form-group">
            <label for="join_date">تاريخ الانضمام:</label>
            <input type="text" id="join_date" value="<?php echo $user['join_date']; ?>" disabled>
        </div>

        <div class="form-group">
            <label for="password">كلمة المرور الجديدة (اتركها فارغة إذا لم ترغب في التغيير):</label>
            <input type="password" id="password" name="password">
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                يجب أن تكون 6 أحرف على الأقل
            </small>
        </div>

        <div class="form-group">
            <label for="confirm_password">تأكيد كلمة المرور الجديدة:</label>
            <input type="password" id="confirm_password" name="confirm_password">
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> حفظ التغييرات</button>
    </form>
</div>

<?php include_once '../includes/footer.php'; ?>