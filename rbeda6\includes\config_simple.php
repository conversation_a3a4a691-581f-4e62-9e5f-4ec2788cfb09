<?php
// ملف إعدادات قاعدة البيانات المبسط

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// معلومات الاتصال بقاعدة البيانات
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'avantely_hamza');  // غير هذا حسب بيانات الخادم
define('DB_PASSWORD', 'hamza123456');     // غير هذا حسب بيانات الخادم
define('DB_NAME', 'avantely_club_db');

// محاولة الاتصال بقاعدة البيانات
try {
    $conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    // تعيين ترميز UTF-8
    if ($conn) {
        mysqli_set_charset($conn, "utf8mb4");
    }
    
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من نجاح الاتصال
if (!$conn) {
    die("فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error());
}

// إعدادات عامة
define('SITE_NAME', 'نادي أفانتي');
define('SITE_URL', 'http://hamza.org.ly/');

// مسارات الملفات
define('UPLOAD_PATH', 'uploads/');
define('LOG_PATH', 'error_log/');

// إنشاء مجلدات إذا لم تكن موجودة
if (!is_dir(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}

if (!is_dir(LOG_PATH)) {
    mkdir(LOG_PATH, 0755, true);
}
?>
