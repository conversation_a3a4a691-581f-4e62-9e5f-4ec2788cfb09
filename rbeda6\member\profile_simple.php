<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: ../index.php");
    exit;
}

require_once '../includes/config.php';

$user_id = $_SESSION['user_id'];
$page_title = "الملف الشخصي";

// جلب بيانات العضو
$user_sql = "SELECT * FROM members WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_sql);
$user = mysqli_fetch_assoc($user_result);

if (!$user) {
    echo "خطأ: لم يتم العثور على بيانات العضو";
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['full_name']);
    $phone = trim($_POST['phone_number']);
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);
    
    // التحقق من صحة البيانات
    if (empty($name)) {
        $error = 'الاسم الكامل مطلوب';
    } elseif (!empty($password) && $password !== $confirm_password) {
        $error = 'كلمتا المرور غير متطابقتين';
    } elseif (!empty($password) && strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        // تحديث البيانات
        if (!empty($password)) {
            // تحديث مع كلمة المرور
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $update_sql = "UPDATE members SET full_name = '$name', phone_number = '$phone', password = '$hashed_password' WHERE id = $user_id";
        } else {
            // تحديث بدون كلمة المرور
            $update_sql = "UPDATE members SET full_name = '$name', phone_number = '$phone' WHERE id = $user_id";
        }
        
        if (mysqli_query($conn, $update_sql)) {
            $_SESSION['user_name'] = $name;
            $success = 'تم تحديث الملف الشخصي بنجاح';
            
            // تحديث البيانات المعروضة
            $user['full_name'] = $name;
            $user['phone_number'] = $phone;
        } else {
            $error = 'حدث خطأ أثناء التحديث: ' . mysqli_error($conn);
        }
    }
}

// إحصائيات العضوية
$stats_sql = "SELECT 
                COUNT(*) as total_subscriptions,
                SUM(amount_paid) as total_paid,
                MAX(end_date) as last_subscription_end
              FROM subscriptions 
              WHERE member_id = $user_id";
$stats_result = mysqli_query($conn, $stats_sql);
$stats = mysqli_fetch_assoc($stats_result);

// الاشتراك النشط
$active_sql = "SELECT COUNT(*) as active_count FROM subscriptions WHERE member_id = $user_id AND status = 'active'";
$active_result = mysqli_query($conn, $active_sql);
$active_count = mysqli_fetch_assoc($active_result)['active_count'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نادي أفانتي</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي
            </div>
            <ul class="nav-links">
                <li><a href="dashboard_simple.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="profile_simple.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                <li><a href="subscriptions_simple.php"><i class="fas fa-credit-card"></i> اشتراكاتي</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                <li><span style="color: #fff; margin-right: 15px;"><i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($user['full_name']); ?></span></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <?php if ($error): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="page-header">
            <h2>الملف الشخصي</h2>
            <a href="dashboard_simple.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للوحة التحكم</a>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
            <!-- معلومات الحساب -->
            <div class="profile-info">
                <h3><i class="fas fa-user"></i> معلومات الحساب</h3>
                
                <div class="info-row">
                    <span class="info-label">الاسم الكامل:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['full_name']); ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">البريد الإلكتروني:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value"><?php echo $user['phone_number'] ? htmlspecialchars($user['phone_number']) : 'غير محدد'; ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ الانضمام:</span>
                    <span class="info-value"><?php echo $user['join_date']; ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">حالة العضوية:</span>
                    <span class="info-value">
                        <span class="status <?php echo $user['status']; ?>">
                            <?php 
                            switch($user['status']) {
                                case 'active': echo 'نشط'; break;
                                case 'inactive': echo 'غير نشط'; break;
                                case 'suspended': echo 'موقوف'; break;
                            }
                            ?>
                        </span>
                    </span>
                </div>
            </div>
            
            <!-- إحصائيات العضوية -->
            <div class="profile-info">
                <h3><i class="fas fa-chart-line"></i> إحصائيات العضوية</h3>
                
                <div class="info-row">
                    <span class="info-label">إجمالي الاشتراكات:</span>
                    <span class="info-value"><?php echo $stats['total_subscriptions'] ?: 0; ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الاشتراكات النشطة:</span>
                    <span class="info-value"><?php echo $active_count; ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">إجمالي المدفوعات:</span>
                    <span class="info-value"><?php echo number_format($stats['total_paid'] ?: 0, 2); ?> ر.س</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">آخر اشتراك ينتهي:</span>
                    <span class="info-value"><?php echo $stats['last_subscription_end'] ?: 'لا يوجد'; ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">مدة العضوية:</span>
                    <span class="info-value">
                        <?php 
                        $join_date = new DateTime($user['join_date']);
                        $today = new DateTime();
                        $interval = $join_date->diff($today);
                        echo $interval->y . ' سنة و ' . $interval->m . ' شهر';
                        ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- نموذج تحديث البيانات -->
        <div class="form-container">
            <h3><i class="fas fa-edit"></i> تحديث البيانات الشخصية</h3>
            
            <form method="POST">
                <div class="form-group">
                    <label for="full_name">الاسم الكامل: *</label>
                    <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" disabled>
                    <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                        لا يمكن تغيير البريد الإلكتروني. اتصل بالإدارة إذا كنت تحتاج لتغييره.
                    </small>
                </div>

                <div class="form-group">
                    <label for="phone_number">رقم الهاتف:</label>
                    <input type="tel" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($user['phone_number']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="join_date">تاريخ الانضمام:</label>
                    <input type="text" id="join_date" value="<?php echo $user['join_date']; ?>" disabled>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور الجديدة (اتركها فارغة إذا لم ترغب في التغيير):</label>
                    <input type="password" id="password" name="password">
                    <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                        يجب أن تكون 6 أحرف على الأقل
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">تأكيد كلمة المرور الجديدة:</label>
                    <input type="password" id="confirm_password" name="confirm_password">
                </div>
                
                <button type="submit" class="btn"><i class="fas fa-save"></i> حفظ التغييرات</button>
            </form>
        </div>

    </div>

    <footer style="background-color: var(--secondary-color); color: white; text-align: center; padding: 20px 0; margin-top: 50px;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> نادي أفانتي. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
</body>
</html>

<style>
.profile-info {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.profile-info h3 {
    color: var(--secondary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: bold;
    color: var(--secondary-color);
}

.info-value {
    color: #333;
}

@media (max-width: 768px) {
    .container > div[style*="grid"] {
        grid-template-columns: 1fr !important;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>
