<?php
$page_title = "إدارة الأعضاء";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// معالجة حذف العضو
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $member_id = (int)$_GET['delete'];

    // التأكد من عدم حذف المدير الحالي
    if ($member_id != $_SESSION['user_id']) {
        $delete_sql = "DELETE FROM members WHERE id = ? AND is_admin = 0";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $member_id);

        if ($delete_stmt->execute()) {
            $_SESSION['success'] = "تم حذف العضو بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء حذف العضو";
        }
        $delete_stmt->close();
    } else {
        $_SESSION['error'] = "لا يمكن حذف حسابك الخاص";
    }

    redirect('index.php');
    exit;
}

// معالجة تغيير حالة العضو
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $member_id = (int)$_GET['toggle_status'];

    $current_status_sql = "SELECT status FROM members WHERE id = ?";
    $current_stmt = $conn->prepare($current_status_sql);
    $current_stmt->bind_param("i", $member_id);
    $current_stmt->execute();
    $current_result = $current_stmt->get_result();
    $current_member = $current_result->fetch_assoc();

    if ($current_member) {
        $new_status = ($current_member['status'] == 'active') ? 'inactive' : 'active';

        $update_sql = "UPDATE members SET status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("si", $new_status, $member_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث حالة العضو بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث حالة العضو";
        }
        $update_stmt->close();
    }
    $current_stmt->close();

    redirect('index.php');
    exit;
}

// البحث والفلترة
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR phone_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= 'sss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// عدد الأعضاء
$count_sql = "SELECT COUNT(*) as total FROM members $where_clause";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_members = $count_stmt->get_result()->fetch_assoc()['total'];
$count_stmt->close();

// الصفحات
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;
$total_pages = ceil($total_members / $per_page);

// جلب الأعضاء
$sql = "SELECT * FROM members $where_clause ORDER BY join_date DESC LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$members = $stmt->get_result();
$stmt->close();

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إدارة الأعضاء (<?php echo $total_members; ?> عضو)</h2>
    <a href="add.php" class="btn"><i class="fas fa-plus"></i> إضافة عضو جديد</a>
</div>

<!-- نموذج البحث والفلترة -->
<div class="table-container" style="margin-bottom: 20px;">
    <form method="GET" style="padding: 20px; display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
        <div class="form-group" style="margin-bottom: 0; min-width: 200px;">
            <label for="search">البحث:</label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="الاسم، البريد الإلكتروني، أو رقم الهاتف">
        </div>

        <div class="form-group" style="margin-bottom: 0; min-width: 150px;">
            <label for="status">الحالة:</label>
            <select id="status" name="status">
                <option value="">جميع الحالات</option>
                <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                <option value="suspended" <?php echo $status_filter == 'suspended' ? 'selected' : ''; ?>>موقوف</option>
            </select>
        </div>

        <button type="submit" class="btn"><i class="fas fa-search"></i> بحث</button>
        <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-times"></i> إلغاء</a>
    </form>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الرقم</th>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الهاتف</th>
                <th>تاريخ الانضمام</th>
                <th>الحالة</th>
                <th>النوع</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($members->num_rows > 0): ?>
                <?php while ($member = $members->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $member['id']; ?></td>
                        <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($member['email']); ?></td>
                        <td><?php echo htmlspecialchars($member['phone_number']); ?></td>
                        <td><?php echo $member['join_date']; ?></td>
                        <td>
                            <span class="status <?php echo $member['status']; ?>">
                                <?php
                                switch($member['status']) {
                                    case 'active': echo 'نشط'; break;
                                    case 'inactive': echo 'غير نشط'; break;
                                    case 'suspended': echo 'موقوف'; break;
                                }
                                ?>
                            </span>
                        </td>
                        <td>
                            <?php echo $member['is_admin'] ? '<span style="color: var(--warning-color); font-weight: bold;"><i class="fas fa-crown"></i> مدير</span>' : 'عضو'; ?>
                        </td>
                        <td class="actions">
                            <a href="edit.php?id=<?php echo $member['id']; ?>" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                <i class="fas fa-edit"></i> تعديل
                            </a>

                            <?php if (!$member['is_admin']): ?>
                                <a href="?toggle_status=<?php echo $member['id']; ?>"
                                   class="btn <?php echo $member['status'] == 'active' ? 'btn-danger' : 'btn-success'; ?>"
                                   style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من تغيير حالة هذا العضو؟')">
                                    <i class="fas fa-<?php echo $member['status'] == 'active' ? 'ban' : 'check'; ?>"></i>
                                    <?php echo $member['status'] == 'active' ? 'إيقاف' : 'تفعيل'; ?>
                                </a>

                                <?php if ($member['id'] != $_SESSION['user_id']): ?>
                                    <a href="?delete=<?php echo $member['id']; ?>"
                                       class="btn btn-danger"
                                       style="padding: 5px 10px; font-size: 12px;"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا العضو؟ سيتم حذف جميع بياناته نهائياً!')">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                        <i class="fas fa-users" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                        <p>لا توجد أعضاء مطابقة لمعايير البحث</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- الصفحات -->
<?php if ($total_pages > 1): ?>
    <div style="text-align: center; margin-top: 20px;">
        <?php
        $query_params = $_GET;
        for ($i = 1; $i <= $total_pages; $i++):
            $query_params['page'] = $i;
            $query_string = http_build_query($query_params);
        ?>
            <a href="?<?php echo $query_string; ?>"
               class="btn <?php echo $i == $page ? 'btn-success' : ''; ?>"
               style="margin: 0 5px; padding: 8px 12px;">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
    </div>
<?php endif; ?>

<?php include_once '../../includes/footer.php'; ?>