<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (is_logged_in()) {
    redirect(is_admin() ? 'admin/dashboard.php' : 'member/dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone_number']);
    $password = sanitize_input($_POST['password']);
    $confirm_password = sanitize_input($_POST['confirm_password']);
    $join_date = date('Y-m-d');

    // التحقق من صحة البيانات
    if (empty($name) || empty($email) || empty($password) || empty($confirm_password)) {
        $error = 'جميع الحقول المطلوبة يجب تعبئتها';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمتا المرور غير متطابقتين';
    } else {
        // التحقق من عدم وجود بريد إلكتروني مكرر
        $check_sql = "SELECT id FROM members WHERE email = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'البريد الإلكتروني مسجل مسبقاً';
        } else {
            // تشفير كلمة المرور
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // إدخال العضو الجديد
            $insert_sql = "INSERT INTO members (full_name, email, password, phone_number, join_date, status, is_admin)
                          VALUES (?, ?, ?, ?, ?, 'active', 0)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("sssss", $name, $email, $hashed_password, $phone, $join_date);

            if ($insert_stmt->execute()) {
                $success = 'تم تسجيل العضو بنجاح! يمكنك الآن <a href="index.php">تسجيل الدخول</a>';
            } else {
                $error = 'حدث خطأ أثناء التسجيل: ' . $conn->error;
            }
        }

        $check_stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل جديد - نادي افانتي</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="register-container">
        <h2>تسجيل عضو جديد</h2>
        <?php if ($error): ?>
            <div class="error"><?= $error ?></div>
        <?php elseif ($success): ?>
            <div class="success"><?= $success ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="full_name">الاسم الكامل:</label>
                <input type="text" id="full_name" name="full_name" required>
            </div>

            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="phone_number">رقم الهاتف:</label>
                <input type="tel" id="phone_number" name="phone_number">
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <div class="form-group">
                <label for="confirm_password">تأكيد كلمة المرور:</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>

            <button type="submit" class="btn">تسجيل</button>
        </form>

        <p>لديك حساب بالفعل؟ <a href="index.php">تسجيل الدخول</a></p>
    </div>
</body>
</html>
