<?php
// ملف اختبار صفحة العضو
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار صفحة العضو</h2>";

// اختبار تضمين الملفات
echo "<h3>1. اختبار تضمين الملفات:</h3>";
try {
    require_once '../includes/config.php';
    echo "✅ تم تحميل config.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل config.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    require_once '../includes/functions.php';
    echo "✅ تم تحميل functions.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل functions.php: " . $e->getMessage() . "<br>";
    exit;
}

// اختبار الجلسة
echo "<h3>2. اختبار الجلسة:</h3>";
session_start();

if (isset($_SESSION['user_id'])) {
    echo "✅ المستخدم مسجل الدخول<br>";
    echo "👤 معرف المستخدم: " . $_SESSION['user_id'] . "<br>";
    echo "📧 اسم المستخدم: " . ($_SESSION['user_name'] ?? 'غير محدد') . "<br>";
    echo "🔑 مدير: " . (($_SESSION['is_admin'] ?? false) ? 'نعم' : 'لا') . "<br>";
    
    $user_id = $_SESSION['user_id'];
    
    // اختبار جلب بيانات العضو
    echo "<h3>3. اختبار جلب بيانات العضو:</h3>";
    try {
        $sql = "SELECT * FROM members WHERE id = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
        }
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $user = $stmt->get_result()->fetch_assoc();
        $stmt->close();
        
        if ($user) {
            echo "✅ تم جلب بيانات العضو بنجاح<br>";
            echo "📝 الاسم: " . htmlspecialchars($user['full_name']) . "<br>";
            echo "📧 البريد: " . htmlspecialchars($user['email']) . "<br>";
            echo "📅 تاريخ الانضمام: " . $user['join_date'] . "<br>";
            echo "🔄 الحالة: " . $user['status'] . "<br>";
        } else {
            echo "❌ لم يتم العثور على بيانات العضو<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في جلب بيانات العضو: " . $e->getMessage() . "<br>";
    }
    
    // اختبار جلب الاشتراكات
    echo "<h3>4. اختبار جلب الاشتراكات:</h3>";
    try {
        $subscription_sql = "SELECT s.*, sp.name as plan_name 
                            FROM subscriptions s 
                            JOIN subscription_plans sp ON s.plan_id = sp.id 
                            WHERE s.member_id = ? 
                            ORDER BY s.created_at DESC";
        $sub_stmt = $conn->prepare($subscription_sql);
        if ($sub_stmt) {
            $sub_stmt->bind_param("i", $user_id);
            $sub_stmt->execute();
            $subscriptions = $sub_stmt->get_result();
            
            echo "✅ تم جلب الاشتراكات بنجاح<br>";
            echo "📊 عدد الاشتراكات: " . $subscriptions->num_rows . "<br>";
            
            if ($subscriptions->num_rows > 0) {
                echo "<table border='1' style='margin-top: 10px;'>";
                echo "<tr><th>الخطة</th><th>البداية</th><th>النهاية</th><th>الحالة</th><th>المبلغ</th></tr>";
                while ($sub = $subscriptions->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($sub['plan_name']) . "</td>";
                    echo "<td>" . $sub['start_date'] . "</td>";
                    echo "<td>" . $sub['end_date'] . "</td>";
                    echo "<td>" . $sub['status'] . "</td>";
                    echo "<td>" . number_format($sub['amount_paid'], 2) . " ر.س</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            $sub_stmt->close();
        } else {
            echo "❌ خطأ في إعداد استعلام الاشتراكات<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في جلب الاشتراكات: " . $e->getMessage() . "<br>";
    }
    
    // اختبار جلب المدفوعات
    echo "<h3>5. اختبار جلب المدفوعات:</h3>";
    try {
        $payment_sql = "SELECT * FROM payments WHERE member_id = ? ORDER BY payment_date DESC LIMIT 5";
        $pay_stmt = $conn->prepare($payment_sql);
        if ($pay_stmt) {
            $pay_stmt->bind_param("i", $user_id);
            $pay_stmt->execute();
            $payments = $pay_stmt->get_result();
            
            echo "✅ تم جلب المدفوعات بنجاح<br>";
            echo "📊 عدد المدفوعات: " . $payments->num_rows . "<br>";
            
            if ($payments->num_rows > 0) {
                echo "<table border='1' style='margin-top: 10px;'>";
                echo "<tr><th>المبلغ</th><th>التاريخ</th><th>الطريقة</th><th>الحالة</th></tr>";
                while ($payment = $payments->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . number_format($payment['amount'], 2) . " ر.س</td>";
                    echo "<td>" . $payment['payment_date'] . "</td>";
                    echo "<td>" . $payment['payment_method'] . "</td>";
                    echo "<td>" . $payment['status'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            $pay_stmt->close();
        } else {
            echo "❌ خطأ في إعداد استعلام المدفوعات<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في جلب المدفوعات: " . $e->getMessage() . "<br>";
    }
    
} else {
    echo "⚠️ المستخدم غير مسجل الدخول<br>";
    echo "<p><a href='../index.php'>تسجيل الدخول</a></p>";
}

echo "<hr>";
echo "<p><strong>إذا كانت جميع الاختبارات ناجحة، يمكنك الآن الذهاب لصفحة لوحة التحكم.</strong></p>";
echo "<p><a href='dashboard.php'>الذهاب للوحة التحكم</a></p>";
?>
