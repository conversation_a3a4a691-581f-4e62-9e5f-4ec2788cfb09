<?php
// ملف اختبار الاتصال بقاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار اتصال قاعدة البيانات</h2>";

// اختبار تضمين الملفات
echo "<h3>1. اختبار تضمين الملفات:</h3>";
try {
    require_once 'includes/config.php';
    echo "✅ تم تحميل config.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل config.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    require_once 'includes/functions.php';
    echo "✅ تم تحميل functions.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل functions.php: " . $e->getMessage() . "<br>";
    exit;
}

// اختبار الاتصال بقاعدة البيانات
echo "<h3>2. اختبار الاتصال بقاعدة البيانات:</h3>";
if (isset($conn) && $conn) {
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    // اختبار الجداول
    echo "<h3>3. اختبار وجود الجداول:</h3>";
    $tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];
    
    foreach ($tables as $table) {
        $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if ($result && mysqli_num_rows($result) > 0) {
            echo "✅ جدول $table موجود<br>";
            
            // عدد السجلات
            $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM $table");
            if ($count_result) {
                $count = mysqli_fetch_assoc($count_result)['count'];
                echo "&nbsp;&nbsp;&nbsp;📊 عدد السجلات: $count<br>";
            }
        } else {
            echo "❌ جدول $table غير موجود<br>";
        }
    }
    
    // اختبار الجلسة
    echo "<h3>4. اختبار الجلسة:</h3>";
    session_start();
    echo "✅ تم بدء الجلسة بنجاح<br>";
    
    if (isset($_SESSION['user_id'])) {
        echo "✅ المستخدم مسجل الدخول - ID: " . $_SESSION['user_id'] . "<br>";
        echo "👤 اسم المستخدم: " . ($_SESSION['user_name'] ?? 'غير محدد') . "<br>";
        echo "🔑 مدير: " . (($_SESSION['is_admin'] ?? false) ? 'نعم' : 'لا') . "<br>";
    } else {
        echo "⚠️ المستخدم غير مسجل الدخول<br>";
    }
    
    // اختبار الدوال
    echo "<h3>5. اختبار الدوال:</h3>";
    if (function_exists('is_logged_in')) {
        echo "✅ دالة is_logged_in موجودة - النتيجة: " . (is_logged_in() ? 'مسجل' : 'غير مسجل') . "<br>";
    } else {
        echo "❌ دالة is_logged_in غير موجودة<br>";
    }
    
    if (function_exists('is_admin')) {
        echo "✅ دالة is_admin موجودة - النتيجة: " . (is_admin() ? 'مدير' : 'عضو عادي') . "<br>";
    } else {
        echo "❌ دالة is_admin غير موجودة<br>";
    }
    
} else {
    echo "❌ فشل الاتصال بقاعدة البيانات<br>";
    echo "تفاصيل الخطأ: " . mysqli_connect_error() . "<br>";
}

echo "<h3>6. معلومات الخادم:</h3>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "إصدار MySQL: " . (isset($conn) ? mysqli_get_server_info($conn) : 'غير متصل') . "<br>";
echo "المجلد الحالي: " . __DIR__ . "<br>";
echo "الملف الحالي: " . __FILE__ . "<br>";

echo "<hr>";
echo "<p><strong>إذا كانت جميع الاختبارات ناجحة، يمكنك حذف هذا الملف والمتابعة لاستخدام النظام.</strong></p>";
echo "<p><a href='index.php'>الذهاب لصفحة تسجيل الدخول</a></p>";
?>
