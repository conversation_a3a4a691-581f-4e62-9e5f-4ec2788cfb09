<?php
// لوحة تحكم المدير - نسخة أساسية جداً
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: ../index_simple.php");
    exit;
}

// التحقق من صلاحيات المدير
if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../member/dashboard_simple.php");
    exit;
}

$page_title = "لوحة تحكم المدير";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نادي أفانتي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .navbar .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .logo i {
            margin-left: 10px;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .main-content {
            padding: 30px 0;
        }
        
        .welcome-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .welcome-card h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .welcome-card p {
            color: #666;
            font-size: 16px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-card p {
            color: #666;
            font-size: 14px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .action-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: transform 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            text-decoration: none;
            color: inherit;
        }
        
        .action-card i {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .action-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .action-card p {
            color: #666;
            font-size: 14px;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .nav-links {
                flex-direction: column;
                gap: 10px;
            }
            
            .navbar .container {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي - لوحة المدير
            </div>
            <ul class="nav-links">
                <li><a href="dashboard_basic.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="../logout_simple.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                <li><span style="color: #fff;"><i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['user_name']); ?></span></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <div class="main-content">
            <?php if (isset($_SESSION['error'])): ?>
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['success'])): ?>
                <div class="success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>

            <div class="welcome-card">
                <h1>مرحباً بك، <?php echo htmlspecialchars($_SESSION['user_name']); ?></h1>
                <p>هنا يمكنك إدارة النادي والتحكم في الأعضاء والاشتراكات</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-users" style="color: #28a745;"></i>
                    <h3>إجمالي الأعضاء</h3>
                    <div class="number">-</div>
                    <p>يتم تحميل البيانات...</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-money-bill-wave" style="color: #17a2b8;"></i>
                    <h3>الإيرادات الشهرية</h3>
                    <div class="number">-</div>
                    <p>يتم تحميل البيانات...</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-credit-card" style="color: #ffc107;"></i>
                    <h3>الاشتراكات النشطة</h3>
                    <div class="number">-</div>
                    <p>يتم تحميل البيانات...</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                    <h3>تنتهي قريباً</h3>
                    <div class="number">-</div>
                    <p>يتم تحميل البيانات...</p>
                </div>
            </div>

            <h2 style="margin-bottom: 20px; color: #333;">الإجراءات السريعة</h2>
            
            <div class="actions-grid">
                <div class="action-card">
                    <i class="fas fa-user-plus"></i>
                    <h3>إضافة عضو جديد</h3>
                    <p>تسجيل عضو جديد في النادي</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-users"></i>
                    <h3>إدارة الأعضاء</h3>
                    <p>عرض وتعديل بيانات الأعضاء</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-plus-circle"></i>
                    <h3>إضافة خطة اشتراك</h3>
                    <p>إنشاء خطة اشتراك جديدة</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-credit-card"></i>
                    <h3>إدارة الاشتراكات</h3>
                    <p>عرض وإدارة خطط الاشتراك</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-money-bill-wave"></i>
                    <h3>تسجيل دفعة</h3>
                    <p>إضافة دفعة جديدة للنظام</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-money-bill"></i>
                    <h3>إدارة المدفوعات</h3>
                    <p>عرض وإدارة المدفوعات</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>عرض التقارير</h3>
                    <p>مراجعة التقارير والإحصائيات</p>
                </div>
                
                <div class="action-card">
                    <i class="fas fa-cog"></i>
                    <h3>إعدادات النظام</h3>
                    <p>تخصيص إعدادات النادي</p>
                </div>
            </div>

            <div style="background: white; padding: 20px; border-radius: 10px; margin-top: 30px; text-align: center;">
                <h3 style="color: #333; margin-bottom: 15px;">روابط مفيدة</h3>
                <p>
                    <a href="../debug.php" style="color: #667eea; text-decoration: none; margin: 0 10px;">تشخيص النظام</a> |
                    <a href="../test_connection.php" style="color: #667eea; text-decoration: none; margin: 0 10px;">اختبار الاتصال</a> |
                    <a href="../setup_database.php" style="color: #667eea; text-decoration: none; margin: 0 10px;">إعداد قاعدة البيانات</a>
                </p>
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> نادي أفانتي. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>
