<?php
$page_title = "إضافة خطة اشتراك جديدة";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name']);
    $description = sanitize_input($_POST['description']);
    $duration_months = (int)$_POST['duration_months'];
    $price = (float)$_POST['price'];
    $discount_percentage = (float)$_POST['discount_percentage'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // التحقق من صحة البيانات
    if (empty($name) || $duration_months <= 0 || $price <= 0) {
        $error = 'جميع الحقول المطلوبة يجب تعبئتها بقيم صحيحة';
    } elseif ($discount_percentage < 0 || $discount_percentage > 100) {
        $error = 'نسبة الخصم يجب أن تكون بين 0 و 100';
    } else {
        // التحقق من عدم وجود خطة بنفس الاسم
        $check_sql = "SELECT id FROM subscription_plans WHERE name = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $name);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'يوجد خطة اشتراك بنفس هذا الاسم مسبقاً';
        } else {
            // إدخال خطة الاشتراك الجديدة
            $insert_sql = "INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage, is_active)
                          VALUES (?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("ssiddi", $name, $description, $duration_months, $price, $discount_percentage, $is_active);

            if ($insert_stmt->execute()) {
                $_SESSION['success'] = 'تم إضافة خطة الاشتراك بنجاح';
                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء إضافة خطة الاشتراك: ' . $conn->error;
            }
            $insert_stmt->close();
        }
        $check_stmt->close();
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إضافة خطة اشتراك جديدة</h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="name">اسم خطة الاشتراك: *</label>
            <input type="text" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                مثال: اشتراك شهري، اشتراك ربع سنوي، اشتراك سنوي
            </small>
        </div>

        <div class="form-group">
            <label for="description">وصف الخطة:</label>
            <textarea id="description" name="description" rows="3"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                وصف مختصر لمميزات هذه الخطة
            </small>
        </div>

        <div class="form-group">
            <label for="duration_months">مدة الاشتراك (بالأشهر): *</label>
            <input type="number" id="duration_months" name="duration_months" min="1" max="60" value="<?php echo isset($_POST['duration_months']) ? $_POST['duration_months'] : '1'; ?>" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                عدد الأشهر التي تغطيها هذه الخطة
            </small>
        </div>

        <div class="form-group">
            <label for="price">السعر الأساسي (ر.س): *</label>
            <input type="number" id="price" name="price" min="0" step="0.01" value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                السعر قبل تطبيق الخصم
            </small>
        </div>

        <div class="form-group">
            <label for="discount_percentage">نسبة الخصم (%):</label>
            <input type="number" id="discount_percentage" name="discount_percentage" min="0" max="100" step="0.01" value="<?php echo isset($_POST['discount_percentage']) ? $_POST['discount_percentage'] : '0'; ?>">
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                نسبة الخصم المطبقة على السعر الأساسي (0 = بدون خصم)
            </small>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" value="1" <?php echo (isset($_POST['is_active']) && $_POST['is_active']) || !isset($_POST['is_active']) ? 'checked' : ''; ?>>
                خطة نشطة ومتاحة للاشتراك
            </label>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                إذا لم تكن محددة، لن تظهر الخطة للأعضاء
            </small>
        </div>

        <!-- معاينة السعر النهائي -->
        <div class="form-group" style="background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
            <h4 style="margin-bottom: 10px; color: var(--secondary-color);">معاينة السعر:</h4>
            <div id="price-preview">
                <p><strong>السعر الأساسي:</strong> <span id="base-price">0.00</span> ر.س</p>
                <p><strong>الخصم:</strong> <span id="discount-amount">0.00</span> ر.س (<span id="discount-percent">0</span>%)</p>
                <p style="font-size: 18px; color: var(--primary-color);"><strong>السعر النهائي:</strong> <span id="final-price">0.00</span> ر.س</p>
            </div>
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> إضافة خطة الاشتراك</button>
    </form>
</div>

<script>
// تحديث معاينة السعر عند تغيير القيم
function updatePricePreview() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const discountAmount = price * (discountPercent / 100);
    const finalPrice = price - discountAmount;

    document.getElementById('base-price').textContent = price.toFixed(2);
    document.getElementById('discount-amount').textContent = discountAmount.toFixed(2);
    document.getElementById('discount-percent').textContent = discountPercent.toFixed(1);
    document.getElementById('final-price').textContent = finalPrice.toFixed(2);
}

document.getElementById('price').addEventListener('input', updatePricePreview);
document.getElementById('discount_percentage').addEventListener('input', updatePricePreview);

// تحديث المعاينة عند تحميل الصفحة
updatePricePreview();
</script>

<?php include_once '../../includes/footer.php'; ?>