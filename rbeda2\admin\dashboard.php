<?php
$page_title = "لوحة التحكم";
$require_admin = true;
require_once '../includes/config.php';
require_once '../includes/auth.php';

// استعلامات للحصول على الإحصائيات
$members_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members"))['count'];
$active_members = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'active'"))['count'];
$revenue = mysqli_fetch_assoc(mysqli_query($conn, "SELECT SUM(amount) as total FROM payments"))['total'] ?? 0;

include_once '../includes/header.php';
?>

<div class="dashboard-header">
    <h1>مرحباً بك، <?php echo $_SESSION['user_name']; ?></h1>
    <p>هنا يمكنك إدارة النادي والتحكم في الأعضاء والاشتراكات.</p>
</div>

<div class="dashboard-cards">
    <div class="card">
        <h3>إجمالي الأعضاء</h3>
        <div class="number"><?php echo $members_count; ?></div>
    </div>
    <div class="card">
        <h3>الأعضاء النشطين</h3>
        <div class="number"><?php echo $active_members; ?></div>
    </div>
    <div class="card">
        <h3>الإيرادات الشهرية</h3>
        <div class="number"><?php echo number_format($revenue); ?> ر.س</div>
    </div>
    <div class="card">
        <h3>الاشتراكات المنتهية</h3>
        <div class="number">12</div>
    </div>
</div>

<div class="page-header">
    <h2>آخر الأعضاء المسجلين</h2>
    <a href="members/" class="btn">عرض الكل</a>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الهاتف</th>
                <th>تاريخ الانضمام</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $sql = "SELECT * FROM members ORDER BY join_date DESC LIMIT 5";
            $result = mysqli_query($conn, $sql);
            
            while ($row = mysqli_fetch_assoc($result)) {
                echo "<tr>
                    <td>{$row['full_name']}</td>
                    <td>{$row['email']}</td>
                    <td>{$row['phone_number']}</td>
                    <td>{$row['join_date']}</td>
                    <td><span class='status {$row['status']}'>";
                
                if ($row['status'] == 'active') echo 'نشط';
                elseif ($row['status'] == 'inactive') echo 'غير نشط';
                else echo 'موقوف';
                
                echo "</span></td>
                </tr>";
            }
            ?>
        </tbody>
    </table>
</div>

<?php include_once '../includes/footer.php'; ?>