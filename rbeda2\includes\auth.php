<?php
// ملف التحقق من الجلسة
session_start();

// تحديد المسار الصحيح لملفات includes
$current_dir = dirname(__FILE__);
$config_path = $current_dir . '/config.php';
$functions_path = $current_dir . '/functions.php';

require_once $config_path;
require_once $functions_path;

// تحديد المسار الصحيح لصفحة تسجيل الدخول
$login_page = 'index.php';
if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
    $login_page = '../../index.php';
} elseif (strpos($_SERVER['REQUEST_URI'], '/member/') !== false) {
    $login_page = '../index.php';
}

// إذا لم يكن المستخدم مسجل الدخول، قم بتوجيهه إلى صفحة تسجيل الدخول
if (!is_logged_in()) {
    redirect($login_page);
    exit;
}

// التحقق من صلاحيات المسؤول
if (isset($require_admin) && $require_admin && !is_admin()) {
    $_SESSION['error'] = "ليس لديك صلاحيات للوصول لهذه الصفحة";

    // تحديد المسار الصحيح لصفحة العضو
    $member_dashboard = 'member/dashboard.php';
    if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
        $member_dashboard = '../member/dashboard.php';
    }

    redirect($member_dashboard);
    exit;
}
?>