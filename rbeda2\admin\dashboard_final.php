<?php
session_start();

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php");
    exit;
}

require_once '../includes/config.php';

$page_title = "لوحة التحكم";

// جلب الإحصائيات
$members_count = 0;
$active_members = 0;
$revenue = 0;
$monthly_revenue = 0;
$active_subscriptions = 0;
$expiring_subscriptions = 0;

try {
    // عدد الأعضاء
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM members");
    if ($result) $members_count = mysqli_fetch_assoc($result)['count'];
    
    // الأعضاء النشطين
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'active'");
    if ($result) $active_members = mysqli_fetch_assoc($result)['count'];
    
    // إجمالي الإيرادات
    $result = mysqli_query($conn, "SELECT SUM(amount) as total FROM payments WHERE status = 'completed'");
    if ($result) $revenue = mysqli_fetch_assoc($result)['total'] ?? 0;
    
    // الإيرادات الشهرية
    $result = mysqli_query($conn, "SELECT SUM(amount) as total FROM payments WHERE status = 'completed' AND MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE())");
    if ($result) $monthly_revenue = mysqli_fetch_assoc($result)['total'] ?? 0;
    
    // الاشتراكات النشطة
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'");
    if ($result) $active_subscriptions = mysqli_fetch_assoc($result)['count'];
    
    // الاشتراكات المنتهية قريباً
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active' AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)");
    if ($result) $expiring_subscriptions = mysqli_fetch_assoc($result)['count'];
    
} catch (Exception $e) {
    // في حالة الخطأ، استخدم القيم الافتراضية
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نادي أفانتي</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي
            </div>
            <ul class="nav-links">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="members/"><i class="fas fa-users"></i> الأعضاء</a></li>
                <li><a href="subscriptions/"><i class="fas fa-credit-card"></i> الاشتراكات</a></li>
                <li><a href="payments/"><i class="fas fa-money-bill"></i> المدفوعات</a></li>
                <li><a href="reports/"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                <li><span style="color: #fff; margin-right: 15px;"><i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['user_name']); ?></span></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <?php if (isset($_SESSION['error'])): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <div class="dashboard-header">
            <h1>مرحباً بك، <?php echo htmlspecialchars($_SESSION['user_name']); ?></h1>
            <p>هنا يمكنك إدارة النادي والتحكم في الأعضاء والاشتراكات.</p>
        </div>

        <div class="dashboard-cards">
            <div class="card">
                <h3>إجمالي الأعضاء</h3>
                <div class="number"><?php echo $members_count; ?></div>
                <p>النشطين: <?php echo $active_members; ?></p>
            </div>
            <div class="card">
                <h3>الإيرادات الشهرية</h3>
                <div class="number" style="color: var(--success-color);"><?php echo number_format($monthly_revenue, 2); ?> ر.س</div>
                <p>الإجمالي: <?php echo number_format($revenue, 2); ?> ر.س</p>
            </div>
            <div class="card">
                <h3>الاشتراكات النشطة</h3>
                <div class="number"><?php echo $active_subscriptions; ?></div>
                <p>تنتهي قريباً: <?php echo $expiring_subscriptions; ?></p>
            </div>
            <div class="card">
                <h3>الاشتراكات المنتهية قريباً</h3>
                <div class="number" style="color: var(--warning-color);"><?php echo $expiring_subscriptions; ?></div>
                <p>خلال 7 أيام</p>
            </div>
        </div>

        <div class="page-header">
            <h2>الإجراءات السريعة</h2>
        </div>

        <div class="quick-actions">
            <a href="members/add.php" class="action-card">
                <i class="fas fa-user-plus"></i>
                <h3>إضافة عضو جديد</h3>
                <p>تسجيل عضو جديد في النادي</p>
            </a>
            
            <a href="subscriptions/add.php" class="action-card">
                <i class="fas fa-plus-circle"></i>
                <h3>إضافة خطة اشتراك</h3>
                <p>إنشاء خطة اشتراك جديدة</p>
            </a>
            
            <a href="payments/add.php" class="action-card">
                <i class="fas fa-money-bill-wave"></i>
                <h3>تسجيل دفعة</h3>
                <p>إضافة دفعة جديدة للنظام</p>
            </a>
            
            <a href="reports/" class="action-card">
                <i class="fas fa-chart-line"></i>
                <h3>عرض التقارير</h3>
                <p>مراجعة التقارير والإحصائيات</p>
            </a>
        </div>

        <div class="page-header">
            <h2>آخر الأنشطة</h2>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <!-- أحدث الأعضاء -->
            <div class="table-container">
                <h3 style="padding: 20px; margin: 0; border-bottom: 1px solid #eee;">أحدث الأعضاء</h3>
                <table>
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>تاريخ الانضمام</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $recent_members_sql = "SELECT full_name, join_date, status FROM members ORDER BY join_date DESC LIMIT 5";
                        $recent_members_result = mysqli_query($conn, $recent_members_sql);
                        
                        if ($recent_members_result && mysqli_num_rows($recent_members_result) > 0):
                            while ($member = mysqli_fetch_assoc($recent_members_result)):
                        ?>
                            <tr>
                                <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                                <td><?php echo $member['join_date']; ?></td>
                                <td>
                                    <span class="status <?php echo $member['status']; ?>">
                                        <?php 
                                        switch($member['status']) {
                                            case 'active': echo 'نشط'; break;
                                            case 'inactive': echo 'غير نشط'; break;
                                            case 'suspended': echo 'موقوف'; break;
                                        }
                                        ?>
                                    </span>
                                </td>
                            </tr>
                        <?php 
                            endwhile;
                        else: 
                        ?>
                            <tr>
                                <td colspan="3" style="text-align: center; padding: 20px;">لا توجد بيانات</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- أحدث المدفوعات -->
            <div class="table-container">
                <h3 style="padding: 20px; margin: 0; border-bottom: 1px solid #eee;">أحدث المدفوعات</h3>
                <table>
                    <thead>
                        <tr>
                            <th>العضو</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $recent_payments_sql = "SELECT p.amount, p.payment_date, m.full_name 
                                               FROM payments p 
                                               JOIN members m ON p.member_id = m.id 
                                               WHERE p.status = 'completed'
                                               ORDER BY p.payment_date DESC 
                                               LIMIT 5";
                        $recent_payments_result = mysqli_query($conn, $recent_payments_sql);
                        
                        if ($recent_payments_result && mysqli_num_rows($recent_payments_result) > 0):
                            while ($payment = mysqli_fetch_assoc($recent_payments_result)):
                        ?>
                            <tr>
                                <td><?php echo htmlspecialchars($payment['full_name']); ?></td>
                                <td><?php echo number_format($payment['amount'], 2); ?> ر.س</td>
                                <td><?php echo $payment['payment_date']; ?></td>
                            </tr>
                        <?php 
                            endwhile;
                        else: 
                        ?>
                            <tr>
                                <td colspan="3" style="text-align: center; padding: 20px;">لا توجد بيانات</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <?php include_once '../includes/footer.php'; ?>
    
    <script src="../assets/js/script.js"></script>
</body>
</html>
